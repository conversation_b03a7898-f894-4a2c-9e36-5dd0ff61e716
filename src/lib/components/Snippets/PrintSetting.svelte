<script lang="ts">
	import { onMount } from 'svelte';
	import { PrinterService } from '$lib/db/PrinterService';
	import { executeAsk, executeMessage } from '$lib/Functions';
	import { saveAs } from 'file-saver';

	const db = new PrinterService();

	let settingNameElement: HTMLElement;
	let settingName = $state('기본(default)');
	let settingNames: string[] = $state([]);
	let printSettings = $state([]);

	async function loadSettings() {
		if (!settingName.trim()) {
			await executeMessage('새로운 설정의 이름을 입력해주세요.');
			settingNameElement.focus();
			return;
		}

		try {
			printSettings = await db.getSettings(settingName);
		} catch (error) {
			console.error(error);
			await executeMessage('설정을 불러오는데 실패했습니다.', 'error');
		}
	}

	async function handleSubmit() {
		if (!settingName.trim()) {
			await executeMessage('설정의 이름을 입력해주세요.');
			return;
		}

		try {
			await db.saveSettings(settingName, printSettings);

			// 새로운 설정이면 목록에 추가
			if (!settingNames.includes(settingName)) {
				settingNames = [...settingNames, settingName];
			}
			await executeMessage("설정이 저장 되었습니다.");
		} catch (error) {
			await executeMessage('설정 저장에 실패했습니다' + error.message, 'error');
		}
	}

	async function handleDelete() {
		try {
			const answer = await executeAsk("정말 이 설정을 삭제하시겠습니까?");
			if (!answer) {
				return;
			}

			await db.deleteSettings(settingName);
			// 설정 목록 다시 불러오기
			settingNames = await db.getAllSettings();
			// 선택된 설정을 새 설정으로 초기화
			settingName = "";
			// 성공 메시지 표시
			await executeMessage('설정이 삭제되었습니다.');
		} catch (error: any) {
			await executeMessage('설정 삭제 중 오류가 발생했습니다: ' + error.message, 'error');
		}
	}

	// 백업 함수 추가
	async function handleBackup() {
		try {
			// 모든 설정 데이터 가져오기
			const allSettings = {};
			for (const name of settingNames) {
				const setting = await db.getSettings(name);
				allSettings[name] = setting;
			}

			// JSON 파일로 변환하여 다운로드
			const blob = new Blob([JSON.stringify(allSettings, null, 2)], {
				type: 'application/json'
			});
			saveAs(blob, 'barcode-settings-backup.json');

			await executeMessage('설정이 백업되었습니다.');
		} catch (error: any) {
			await executeMessage('백업 중 오류가 발생했습니다: ' + error.message, 'error');
		}
	}

	// 복구 함수 추가
	async function handleRestore(event: Event) {
		try {
			const file = (event.target as HTMLInputElement).files?.[0];
			if (!file) return;

			const reader = new FileReader();
			reader.onload = async (e) => {
				try {
					const settings = JSON.parse(e.target?.result as string);

					// 각 설정을 IndexedDB에 저장
					for (const [name, data] of Object.entries(settings)) {
						await db.saveSettings(name, data as any[]);
					}

					// 설정 목록 새로고침
					settingNames = await db.getAllSettings();
					await executeMessage('설정이 복구되었습니다.');
				} catch (error) {
					await executeMessage('설정 파일 복구 중 오류가 발생했습니다.', 'error');
				}
			};
			reader.readAsText(file);
		} catch (error: any) {
			await executeMessage('파일 읽기 중 오류가 발생했습니다: ' + error.message, 'error');
		}
	}

	onMount(async () => {
		await db.initDatabase();
		settingNames = await db.getAllSettings();
		printSettings = await db.getSettings(settingName);
	});
</script>

<div class="w-96 flex flex-col">
	<div class="w-full mb-4">
		<div class="form-control w-full max-w-xs">
			<div class="label">
				<span class="label-text">설정 이름</span>
			</div>
			<div class="flex gap-2">
				<select
					bind:value={settingName}
					class="select select-bordered select-sm"
					onchange={loadSettings}>
					<option value="">새 설정</option>
					{#each settingNames as name}
						<option value={name}>{name}</option>
					{/each}
				</select>
				{#if !settingNames.includes(settingName)}
					<input
						type="text"
						placeholder="새 설정 이름 입력"
						class="input input-bordered input-sm w-40"
						bind:value={settingName}
						bind:this={settingNameElement}
					/>
				{/if}
			</div>
			<div class="flex gap-2 mt-2 items-center justify-center">
				<!-- 백업/복구 버튼 추가 -->
				<button class="btn btn-sm btn-primary" onclick={handleBackup}>
					백업
				</button>
				<label class="btn btn-sm btn-secondary">
					복구
					<input
						accept=".json"
						onchange={handleRestore}
						style="display: none;"
						type="file"
					/>
				</label>
				{#if settingNames.includes(settingName) && settingName !== "기본(default)"}
					<button
						class="btn btn-sm btn-error"
						onclick={handleDelete}>
						삭제
					</button>
				{/if}
			</div>
		</div>
	</div>
	
	{#if printSettings && printSettings.length > 0}
		<div role="alert" class="alert alert-info mb-3">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 24 24"
				class="h-6 w-6 shrink-0 stroke-current">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
			</svg>
			<span>설정을 만들거나 수정했다면 하단에 저장 버튼을 클릭해 꼭 저장해 주세요.</span>
		</div>
		
		{#each printSettings as group, index}
			<div class="form-control">
				{#if group?.settings}
					<h2 class="font-bold">{group.name}</h2>
					<div>
						<!--폰트 설정-->
						{#if 'fontFamily' in group.settings}
							<div class="flex gap-4 items-center">
								<label class="flex items-center gap-2">
									<input type="radio"
												 name="fontFamily"
												 value="NotoSansKR"
												 class="radio radio-sm"
												 bind:group={printSettings[index].settings.fontFamily}
									>
									<span>한글 지원</span>
								</label>
								<label class="flex items-center gap-2">
									<input type="radio"
												 name="fontFamily"
												 value="NotoSansTC"
												 class="radio radio-sm"
												 bind:group={printSettings[index].settings.fontFamily}
									>
									<span>대만어 지원</span>
								</label>
							</div>
						{/if}
						<!-- 숨김, 두껍게 -->
						{#if 'hidden' in group.settings || 'bold' in group.settings}
							<div class="flex gap-4 items-center">
								{#if 'hidden' in group.settings}
									<label class="flex items-center gap-2">
										<input type="checkbox"
													 bind:checked={printSettings[index].settings.hidden}
													 class="checkbox checkbox-sm"
										> <span>숨김</span>
									</label>
								{/if}
								{#if 'bold' in group.settings}
									<label class="flex items-center gap-2">
										<input type="checkbox"
													 bind:checked={printSettings[index].settings.bold}
													 class="checkbox checkbox-sm"
										> <span>두껍게</span>
									</label>
								{/if}
							</div>
						{/if}
						
						<!-- 출력일 포맷 설정 -->
						{#if 'format' in group.settings}
							<div class="mt-2">
								<div class="flex gap-4">
									<label class="flex items-center gap-2">
										<input type="radio"
													 name="dateFormat"
													 value="YY-MM-DD"
													 class="radio radio-sm"
													 bind:group={printSettings[index].settings.format}
										>
										<span>YY-MM-DD</span>
									</label>
									<label class="flex items-center gap-2">
										<input type="radio"
													 name="dateFormat"
													 value="YY-MM"
													 class="radio radio-sm"
													 bind:group={printSettings[index].settings.format}
										>
										<span>YY-MM</span>
									</label>
									<label class="flex items-center gap-2">
										<input type="radio"
													 name="dateFormat"
													 value="MM-DD"
													 class="radio radio-sm"
													 bind:group={printSettings[index].settings.format}
										>
										<span>MM-DD</span>
									</label>
								</div>
							</div>
						{/if}
						
						<!-- 폰트크기 -->
						{#if 'fontSize' in group.settings || 'wrap' in group.settings || 'unit' in group.settings}
							<div class="flex gap-4 items-center">
								{#if 'fontSize' in group.settings}
									<label class="flex items-center gap-2">
										<span>폰트크기:</span>
										<input type="number" bind:value={printSettings[index].settings.fontSize} min="5" max="20" step="1" class="input input-bordered input-sm w-16">
									</label>
								{/if}
								{#if 'unit' in group.settings}
									<label class="flex items-center gap-2">
										<span>단위:</span>
										<input type="text" bind:value={printSettings[index].settings.unit} class="input input-bordered input-sm w-16">
									</label>
								{/if}
							</div>
						{/if}
						
						<!-- X축, Y축 -->
						{#if 'x' in group.settings || 'y' in group.settings}
							<div class="flex gap-4 items-center">
								{#if 'x' in group.settings}
									<label class="flex items-center gap-2">
										<span>X축:</span>
										<input type="number"
													 bind:value={printSettings[index].settings.x}
													 min="0"
													 max="150"
													 step="1"
													 class="input input-bordered input-sm w-16">
									</label>
								{/if}
								{#if 'y' in group.settings}
									<label class="flex items-center gap-2">
										<span>Y축:</span>
										<input type="number"
													 bind:value={printSettings[index].settings.y}
													 min="1"
													 max="100"
													 step="1"
													 class="input input-bordered input-sm w-16">
									</label>
								{/if}
							</div>
						{/if}
						
						<!-- 가로, 세로 -->
						{#if 'width' in group.settings && 'height' in group.settings}
							<div class="flex gap-4 items-center">
								<label class="flex items-center gap-2">
									<span>가로 크기:</span>
									<input type="number" bind:value={printSettings[index].settings.width} min="50" max="150" step="1" class="input input-bordered input-sm w-16">
								</label>
								<label class="flex items-center gap-2">
									<span>높이:</span>
									<input type="number" bind:value={printSettings[index].settings.height} min="15" max="40" step="1" class="input input-bordered input-sm w-16">
								</label>
							</div>
						{/if}
					</div>
					
					<div class="divider"></div>
				{/if}
			</div>
		{/each}
		
		<div class="w-full flex justify-center">
			<button class="btn btn-primary"
							onclick={handleSubmit}
							type="button"
			>
				저장
			</button>
		</div>
	{:else}
		<div>바코드 설정을 불러오는 중입니다...</div>
	{/if}

</div>